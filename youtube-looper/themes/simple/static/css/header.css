/* YouTube Looper - Head<PERSON> Styles */

/* Header - Scrollable, not fixed */
header {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(139, 92, 246, 0.35) 25%,
    rgba(59, 130, 246, 0.30) 75%,
    rgba(255, 255, 255, 0.20) 100%
  );
  backdrop-filter: blur(25px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: none;
  padding: 2rem 2.5rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset,
    0 20px 60px rgba(139, 92, 246, 0.15);
  transition: all 0.3s ease;
  max-width: 1400px;
  margin: 0 auto;
  border-radius: 0 0 24px 24px;
  position: relative;
  overflow: hidden;
}

/* Add subtle glow animation */
header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(139, 92, 246, 0.1) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1;
}

header:hover::before {
  opacity: 1;
}



/* Minimized Header */
header.minimized {
  padding: 1.5rem 2rem;
}

/* Header Logo Section */
.header-logo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.25);
  gap: 2rem;
  position: relative;
  z-index: 2;
}

/* Minimized Header Logo */
header.minimized .header-logo {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 2rem;
  font-weight: 800;
  color: white;
  text-decoration: none;
  letter-spacing: -0.02em;
  transition: all 0.3s ease;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Minimized Logo - keep same font size */
header.minimized .logo {
  font-size: 2rem;
}

/* Header Current Playing Info */
.header-current-playing {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  max-width: 600px;
}

.header-control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.header-control-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.35);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.header-playing-info {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 0;
}

#header-current-video-info {
  font-size: 0.95rem;
  color: white;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}

/* Media Control Center - Grid Layout */
.media-control-center {
  display: grid;
  grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
  grid-template-rows: auto;
  gap: 2.5rem;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 2;
}

/* Minimized Media Control Center */
header.minimized .media-control-center {
  display: none;
}

/* Video Player Section within Media Control Center */
.media-control-center > #player {
  grid-column: 1;
  grid-row: 1;
}

#player {
  width: 100%;
  height: 280px;
  border-radius: 16px;
  background: #000;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  overflow: hidden;
  border: 3px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

#player:hover {
  transform: translateY(-2px);
  box-shadow: 0 16px 50px rgba(0, 0, 0, 0.5);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Ensure YouTube player doesn't expand beyond container */
#player iframe,
#player .ytp-embed {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
}



/* Header Current Queue */
.header-current-queue {
  display: grid;
  grid-template-columns: 1fr auto;
  grid-template-rows: auto 1fr;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.25);
  padding: 1rem;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  height: 280px;
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
  grid-column: 2;
  grid-row: 1;
}

.header-current-queue .queue-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: white !important;
  margin: 0;
  grid-column: 1;
  grid-row: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.queue-actions {
  display: flex;
  gap: 0.5rem;
  grid-column: 2;
  grid-row: 1;
}

.queue-count {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.25), rgba(139, 92, 246, 0.2));
  color: #667eea;
  padding: 0.375rem 0.75rem;
  border-radius: 10px;
  font-size: 0.8rem;
  font-weight: 700;
  min-width: 1.75rem;
  text-align: center;
  border: 1px solid rgba(102, 126, 234, 0.3);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
  backdrop-filter: blur(10px);
}



.queue-action-btn {
  width: 32px;
  height: 32px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  font-size: 0.875rem;
}

.queue-action-play {
  background: rgba(16, 185, 129, 0.9);
  color: white;
}

.queue-action-play:hover {
  background: rgba(16, 185, 129, 1);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.queue-action-clear {
  background: rgba(239, 68, 68, 0.9);
  color: white;
}

.queue-action-clear:hover {
  background: rgba(239, 68, 68, 1);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.header-current-queue .queue-container {
  height: 180px !important; /* Fixed height instead of max-height */
  min-height: 0 !important; /* Override sidebar.css min-height: 400px */
  max-height: 180px !important; /* Ensure it doesn't grow */
  overflow-y: auto; /* Allow scrolling but hide scrollbar */
  overflow-x: hidden;
  width: 100%;
  min-width: 0;
  grid-column: 1 / -1;
  grid-row: 2;
  margin-top: 0.75rem;
  padding: 0; /* Remove padding to allow proper centering */
  position: relative;
  /* Hide scrollbar but keep functionality */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
  /* Prevent scroll events from bubbling to parent */
  overscroll-behavior: contain;
  touch-action: none;
  /* Ensure proper flex container for empty state */
  display: flex;
  flex-direction: column;
}

/* Hide scrollbar for WebKit browsers */
.header-current-queue .queue-container::-webkit-scrollbar {
  display: none;
}




.header-current-queue .queue-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  width: 100%;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  /* Remove box styling - no background, border, or padding */
  /* Compensate for container's margin-top to achieve true visual centering */
  margin-top: -0.375rem; /* Half of container's margin-top (0.75rem / 2) */
}

.header-current-queue .queue-empty-icon {
  font-size: 2.5rem;
  margin-bottom: 0.75rem;
  opacity: 0.9;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.header-current-queue .queue-empty p {
  margin: 0.375rem 0;
  line-height: 1.5;
  font-weight: 500;
}

.header-current-queue .queue-empty p:first-of-type {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.95rem;
}

/* Header Minimize Toggle */
.header-minimize-toggle {
  width: 44px;
  height: 44px;
  border: none;
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border-radius: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.header-minimize-toggle:hover {
  background: rgba(30, 41, 59, 0.2);
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.4);
}

/* Minimized state - show expand icon */
header.minimized .header-minimize-toggle svg {
  transform: rotate(180deg);
}

/* Header Queue Items - Override main queue styles for header context */
.header-current-queue .queue-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem !important;
  border-radius: 8px !important;
  margin: 0 0.5rem 0.25rem 0 !important; /* Add right margin for spacing */
  cursor: pointer;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03)) !important;
  border: 1px solid rgba(255, 255, 255, 0.12) !important;
  backdrop-filter: blur(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
  transform: none !important; /* Reset any inherited transforms */
  width: calc(100% - 0.5rem); /* Account for right margin */
  max-width: calc(100% - 0.5rem);
  min-width: 0;
  box-sizing: border-box;
}

.header-current-queue .queue-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(139, 92, 246, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.header-current-queue .queue-item:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08)) !important;
  transform: translateY(-2px) scale(1.01) !important;
  box-shadow:
    0 8px 20px rgba(0, 0, 0, 0.15),
    0 4px 12px rgba(102, 126, 234, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.25) !important;
}

.header-current-queue .queue-item:hover::before {
  opacity: 1;
}

.header-current-queue .queue-item.active {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.25), rgba(139, 92, 246, 0.15)) !important;
  border-color: rgba(102, 126, 234, 0.6) !important;
  box-shadow:
    0 12px 28px rgba(102, 126, 234, 0.25),
    0 6px 16px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-1px) !important;
}

.header-current-queue .queue-item.active::before {
  opacity: 0.7;
}

/* Header Queue Item Thumbnail */
.header-current-queue .queue-item-thumbnail {
  position: relative !important;
  width: 50px !important;
  height: 38px !important;
  border-radius: 6px !important;
  overflow: hidden !important;
  flex-shrink: 0 !important;
  background: linear-gradient(135deg, #667eea, #764ba2) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

.header-current-queue .queue-item-thumbnail img,
.header-current-queue .queue-item-thumbnail .thumbnail-img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  border-radius: 8px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.header-current-queue .queue-item:hover .queue-item-thumbnail img,
.header-current-queue .queue-item:hover .queue-item-thumbnail .thumbnail-img {
  transform: scale(1.05) !important;
  filter: brightness(1.1) saturate(1.2) !important;
}

.header-current-queue .queue-item.active .queue-item-thumbnail img,
.header-current-queue .queue-item.active .queue-item-thumbnail .thumbnail-img {
  transform: scale(1.02) !important;
  filter: brightness(1.15) saturate(1.3) !important;
}

/* Queue Item Number in Header */
.header-current-queue .queue-item-number {
  position: absolute !important;
  top: 3px !important;
  left: 3px !important;
  width: 16px !important;
  height: 16px !important;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7)) !important;
  color: white !important;
  border-radius: 4px !important;
  font-size: 0.6rem !important;
  font-weight: 700 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 2 !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3) !important;
}

.header-current-queue .queue-item.active .queue-item-number {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(139, 92, 246, 0.8)) !important;
  color: white !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

/* Playing Indicator */
.header-current-queue .playing-indicator {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  background: linear-gradient(135deg, #10b981, #059669) !important;
  color: white !important;
  font-size: 0.9rem !important;
  width: 24px !important;
  height: 24px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  animation: headerPlayingPulse 2s infinite !important;
  box-shadow:
    0 3px 12px rgba(16, 185, 129, 0.4),
    0 0 0 3px rgba(16, 185, 129, 0.2) !important;
  border: 2px solid rgba(255, 255, 255, 0.9) !important;
  z-index: 2 !important;
}

@keyframes headerPlayingPulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    box-shadow:
      0 3px 12px rgba(16, 185, 129, 0.4),
      0 0 0 3px rgba(16, 185, 129, 0.2);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    box-shadow:
      0 4px 16px rgba(16, 185, 129, 0.6),
      0 0 0 6px rgba(16, 185, 129, 0.3);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    box-shadow:
      0 3px 12px rgba(16, 185, 129, 0.4),
      0 0 0 3px rgba(16, 185, 129, 0.2);
  }
}

/* Header Queue Item Info */
.header-current-queue .queue-item-info {
  flex: 1 !important;
  min-width: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 0.25rem !important;
  padding-right: 0.5rem !important;
}

.header-current-queue .queue-item-title {
  font-size: 0.9rem !important;
  font-weight: 600 !important;
  color: white !important;
  margin: 0 !important;
  line-height: 1.4 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}
  transition: color 0.3s ease !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1) !important;
}

.header-current-queue .queue-item:hover .queue-item-title {
  color: #0f172a !important;
}

.header-current-queue .queue-item.active .queue-item-title {
  color: #667eea !important;
  font-weight: 700 !important;
}

.header-current-queue .queue-item-meta {
  font-size: 0.75rem !important;
  color: rgba(255, 255, 255, 0.7) !important;
  margin: 0 !important;
  font-weight: 500 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.375rem !important;
}

.header-current-queue .queue-item.active .queue-item-meta {
  color: rgba(255, 255, 255, 0.9) !important;
}
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Header Queue Item Actions */
.header-current-queue .queue-item-actions {
  display: flex !important;
  gap: 0.25rem !important;
  opacity: 0 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  flex-shrink: 0 !important;
  transform: translateX(8px) !important;
}

.header-current-queue .queue-item:hover .queue-item-actions {
  opacity: 1 !important;
  transform: translateX(0) !important;
}

.header-current-queue .queue-item.active .queue-item-actions {
  opacity: 0.7 !important;
  transform: translateX(0) !important;
}

.header-current-queue .queue-btn,
.header-current-queue .play-btn,
.header-current-queue .remove-btn,
.header-current-queue .copy-url-btn {
  width: 30px !important;
  height: 30px !important;
  border: none !important;
  border-radius: 8px !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.1)) !important;
  color: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(15px) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  font-size: 0.8rem !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
  overflow: hidden !important;
}

.header-current-queue .queue-btn::before,
.header-current-queue .play-btn::before,
.header-current-queue .remove-btn::before,
.header-current-queue .copy-url-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.header-current-queue .queue-btn:hover,
.header-current-queue .play-btn:hover,
.header-current-queue .remove-btn:hover,
.header-current-queue .copy-url-btn:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.2)) !important;
  color: white !important;
  transform: translateY(-1px) scale(1.05) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.header-current-queue .queue-btn:hover::before,
.header-current-queue .play-btn:hover::before,
.header-current-queue .remove-btn:hover::before,
.header-current-queue .copy-url-btn:hover::before {
  opacity: 1;
}

.header-current-queue .play-btn:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.4), rgba(16, 185, 129, 0.3)) !important;
  color: #10b981 !important;
  border-color: rgba(16, 185, 129, 0.5) !important;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3) !important;
}

.header-current-queue .copy-url-btn:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.4), rgba(59, 130, 246, 0.3)) !important;
  color: #3b82f6 !important;
  border-color: rgba(59, 130, 246, 0.5) !important;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3) !important;
}

.header-current-queue .remove-btn:hover {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.4), rgba(239, 68, 68, 0.3)) !important;
  color: #ef4444 !important;
  border-color: rgba(239, 68, 68, 0.5) !important;
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3) !important;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .media-control-center {
    gap: 2rem;
  }

  #player {
    height: 240px;
  }

  .header-current-queue {
    height: 240px;
  }

  .header-current-queue .queue-container {
    height: 180px !important;
    min-height: 0 !important;
    max-height: 180px !important;
  }
}

@media (max-width: 1024px) {
  header {
    padding: 1.5rem 2rem;
    border-radius: 0 0 16px 16px;
  }

  .logo {
    font-size: 2rem;
  }

  .header-current-playing {
    max-width: 400px;
    padding: 0.5rem 1rem;
  }

  #header-current-video-info {
    font-size: 0.85rem;
  }

  .media-control-center {
    grid-template-columns: 1fr;
    gap: 2rem;
    width: 100%;
    max-width: 100%;
  }

  .header-current-queue {
    width: 100%;
    max-width: 100%;
    min-width: 0;
  }

  #player {
    height: 220px;
  }

  .header-current-queue {
    height: 220px;
  }

  .header-current-queue .queue-container {
    height: 160px !important;
    min-height: 0 !important;
    max-height: 160px !important;
  }
}

@media (max-width: 768px) {
  header {
    padding: 1rem 1.5rem;
    border-radius: 0 0 12px 12px;
  }

  .header-logo {
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .logo {
    font-size: 2rem;
  }

  .header-current-playing {
    max-width: none;
    padding: 0.5rem 1rem;
    gap: 0.75rem;
  }

  .header-control-btn {
    width: 32px;
    height: 32px;
  }

  #header-current-video-info {
    font-size: 0.8rem;
  }

  .media-control-center {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .header-current-queue {
    padding: 1rem;
  }

  #player {
    height: 180px;
  }

  .header-current-queue {
    height: 180px;
  }

  .header-current-queue .queue-container {
    height: 120px !important;
    min-height: 0 !important;
    max-height: 120px !important;
  }

  /* Mobile queue item adjustments */
  .header-current-queue .queue-item {
    padding: 0.375rem !important;
    gap: 0.375rem !important;
    margin-bottom: 0.2rem !important;
  }

  .header-current-queue .queue-item-thumbnail {
    width: 40px !important;
    height: 30px !important;
  }

  .header-current-queue .queue-item-number {
    width: 14px !important;
    height: 14px !important;
    font-size: 0.55rem !important;
  }

  .header-current-queue .queue-item-title {
    font-size: 0.75rem !important;
  }

  .header-current-queue .queue-item-meta {
    font-size: 0.65rem !important;
  }

  .header-current-queue .queue-btn,
  .header-current-queue .play-btn,
  .header-current-queue .remove-btn {
    width: 24px !important;
    height: 24px !important;
    font-size: 0.7rem !important;
  }

  /* Always show minimized on mobile */
  header.minimized {
    padding: 0.75rem;
  }
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  position: relative;
  transition: all 0.3s ease;
}

.logo-icon::after {
  content: '';
  position: absolute;
  inset: 2px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  border-radius: 14px;
  transition: all 0.3s ease;
}

/* Simple breathing animation for logo when playing */
@keyframes logoBreathing {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
  }
}

/* Apply breathing animation when playing */
.logo-icon.dancing {
  animation: logoBreathing 2s ease-in-out infinite;
}




